const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const fs = require('fs-extra');

const ServerManager = require('./core/ServerManager');
const ConfigManager = require('./core/ConfigManager');

const app = express();
const PORT = 3000;

// 中间件
app.use(cors());
app.use(bodyParser.json());
app.use(express.static(path.join(__dirname, '../public')));

// 初始化管理器
const configManager = new ConfigManager();
const serverManager = new ServerManager(configManager);

// API路由
app.use('/api', require('./routes/api')(serverManager));

// 主页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/index.html'));
});

// 启动管理服务器
app.listen(PORT, () => {
    console.log(`HTTP服务器管理工具运行在 http://localhost:${PORT}`);
    
    // 加载保存的配置
    serverManager.loadConfig();
});

// 优雅关闭
process.on('SIGINT', () => {
    console.log('\n正在关闭服务器...');
    serverManager.stopAllServers();
    process.exit(0);
});
