const express = require('express');
const path = require('path');
const fs = require('fs-extra');

class ServerManager {
    constructor(configManager) {
        this.configManager = configManager;
        this.servers = new Map(); // 存储服务器实例
        this.config = { servers: [] };
    }

    // 加载配置
    loadConfig() {
        this.config = this.configManager.loadConfig();
        // 自动启动之前运行的服务器
        this.config.servers.forEach(serverConfig => {
            if (serverConfig.autoStart) {
                this.startServer(serverConfig.id);
            }
        });
    }

    // 保存配置
    saveConfig() {
        return this.configManager.saveConfig(this.config);
    }

    // 创建新服务器配置
    createServer(serverConfig) {
        const id = this.generateId();
        const newServer = {
            id,
            name: serverConfig.name || `服务器-${id}`,
            port: serverConfig.port,
            pathMappings: serverConfig.pathMappings || [],
            status: 'stopped',
            autoStart: serverConfig.autoStart || false,
            createdAt: new Date().toISOString()
        };

        this.config.servers.push(newServer);
        this.saveConfig();
        return newServer;
    }

    // 获取所有服务器
    getAllServers() {
        return this.config.servers.map(server => ({
            ...server,
            status: this.servers.has(server.id) ? 'running' : 'stopped'
        }));
    }

    // 获取单个服务器
    getServer(id) {
        const server = this.config.servers.find(s => s.id === id);
        if (server) {
            return {
                ...server,
                status: this.servers.has(id) ? 'running' : 'stopped'
            };
        }
        return null;
    }

    // 更新服务器配置
    updateServer(id, updates) {
        const serverIndex = this.config.servers.findIndex(s => s.id === id);
        if (serverIndex === -1) return null;

        // 如果服务器正在运行，先停止它
        if (this.servers.has(id)) {
            this.stopServer(id);
        }

        this.config.servers[serverIndex] = {
            ...this.config.servers[serverIndex],
            ...updates,
            id // 确保ID不被修改
        };

        this.saveConfig();
        return this.config.servers[serverIndex];
    }

    // 删除服务器
    deleteServer(id) {
        // 先停止服务器
        if (this.servers.has(id)) {
            this.stopServer(id);
        }

        const serverIndex = this.config.servers.findIndex(s => s.id === id);
        if (serverIndex === -1) return false;

        this.config.servers.splice(serverIndex, 1);
        this.saveConfig();
        return true;
    }

    // 启动服务器
    startServer(id) {
        const serverConfig = this.config.servers.find(s => s.id === id);
        if (!serverConfig) {
            throw new Error('服务器配置不存在');
        }

        if (this.servers.has(id)) {
            throw new Error('服务器已在运行');
        }

        // 检查端口是否被占用
        if (this.isPortInUse(serverConfig.port)) {
            throw new Error(`端口 ${serverConfig.port} 已被占用`);
        }

        const app = express();
        
        // 配置路径映射
        serverConfig.pathMappings.forEach(mapping => {
            if (fs.existsSync(mapping.localPath)) {
                app.use(mapping.urlPath, express.static(mapping.localPath));
                console.log(`映射: ${mapping.urlPath} -> ${mapping.localPath}`);
            } else {
                console.warn(`警告: 本地路径不存在 ${mapping.localPath}`);
            }
        });

        // 默认处理器
        app.get('/', (req, res) => {
            res.json({
                server: serverConfig.name,
                port: serverConfig.port,
                pathMappings: serverConfig.pathMappings,
                message: '服务器运行正常'
            });
        });

        // 启动服务器
        const server = app.listen(serverConfig.port, () => {
            console.log(`服务器 ${serverConfig.name} 启动在端口 ${serverConfig.port}`);
        });

        this.servers.set(id, server);
        return true;
    }

    // 停止服务器
    stopServer(id) {
        const server = this.servers.get(id);
        if (!server) {
            throw new Error('服务器未运行');
        }

        server.close(() => {
            console.log(`服务器 ${id} 已停止`);
        });
        
        this.servers.delete(id);
        return true;
    }

    // 停止所有服务器
    stopAllServers() {
        this.servers.forEach((server, id) => {
            server.close();
            console.log(`服务器 ${id} 已停止`);
        });
        this.servers.clear();
    }

    // 检查端口是否被使用
    isPortInUse(port) {
        for (let [id, server] of this.servers) {
            const serverConfig = this.config.servers.find(s => s.id === id);
            if (serverConfig && serverConfig.port === port) {
                return true;
            }
        }
        return false;
    }

    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }
}

module.exports = ServerManager;
