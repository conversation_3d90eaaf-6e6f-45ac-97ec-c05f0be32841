class ServerManager {
    constructor() {
        this.servers = [];
        this.currentEditingId = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadServers();
        
        // 定期刷新服务器状态
        setInterval(() => this.loadServers(), 5000);
    }

    bindEvents() {
        // 添加服务器按钮
        document.getElementById('addServerBtn').addEventListener('click', () => {
            this.showModal();
        });

        // 模态框关闭
        document.querySelector('.close').addEventListener('click', () => {
            this.hideModal();
        });

        document.getElementById('cancelBtn').addEventListener('click', () => {
            this.hideModal();
        });

        // 点击模态框外部关闭
        document.getElementById('serverModal').addEventListener('click', (e) => {
            if (e.target.id === 'serverModal') {
                this.hideModal();
            }
        });

        // 表单提交
        document.getElementById('serverForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveServer();
        });

        // 添加路径映射
        document.getElementById('addMappingBtn').addEventListener('click', () => {
            this.addMappingRow();
        });
    }

    async loadServers() {
        try {
            const response = await fetch('/api/servers');
            const result = await response.json();
            
            if (result.success) {
                this.servers = result.data;
                this.renderServers();
            } else {
                this.showError('加载服务器列表失败: ' + result.error);
            }
        } catch (error) {
            this.showError('网络错误: ' + error.message);
        }
    }

    renderServers() {
        const grid = document.getElementById('serversGrid');
        grid.innerHTML = '';

        if (this.servers.length === 0) {
            grid.innerHTML = '<div style="grid-column: 1/-1; text-align: center; padding: 40px; color: #666;">暂无服务器，点击"添加服务器"开始创建</div>';
            return;
        }

        this.servers.forEach(server => {
            const card = this.createServerCard(server);
            grid.appendChild(card);
        });
    }

    createServerCard(server) {
        const template = document.getElementById('serverCardTemplate');
        const card = template.content.cloneNode(true);

        // 填充服务器信息
        card.querySelector('.server-name').textContent = server.name;
        card.querySelector('.server-port').textContent = server.port;
        card.querySelector('.mapping-count').textContent = server.pathMappings.length;

        // 设置状态
        const statusIndicator = card.querySelector('.status-indicator');
        const statusText = card.querySelector('.status-text');
        
        if (server.status === 'running') {
            statusIndicator.classList.add('running');
            statusText.textContent = '运行中';
        } else {
            statusIndicator.classList.add('stopped');
            statusText.textContent = '已停止';
        }

        // 填充路径映射
        const mappingsList = card.querySelector('.mappings-list');
        if (server.pathMappings.length > 0) {
            server.pathMappings.forEach(mapping => {
                const li = document.createElement('li');
                li.innerHTML = `<strong>${mapping.urlPath}</strong> → ${mapping.localPath}`;
                mappingsList.appendChild(li);
            });
        } else {
            mappingsList.innerHTML = '<li style="color: #666;">暂无路径映射</li>';
        }

        // 绑定按钮事件
        const startBtn = card.querySelector('.start-btn');
        const stopBtn = card.querySelector('.stop-btn');
        const editBtn = card.querySelector('.edit-btn');
        const deleteBtn = card.querySelector('.delete-btn');

        // 根据状态显示/隐藏按钮
        if (server.status === 'running') {
            startBtn.style.display = 'none';
        } else {
            stopBtn.style.display = 'none';
        }

        startBtn.addEventListener('click', () => this.startServer(server.id));
        stopBtn.addEventListener('click', () => this.stopServer(server.id));
        editBtn.addEventListener('click', () => this.editServer(server));
        deleteBtn.addEventListener('click', () => this.deleteServer(server.id));

        return card;
    }

    showModal(server = null) {
        const modal = document.getElementById('serverModal');
        const title = document.getElementById('modalTitle');
        const form = document.getElementById('serverForm');

        if (server) {
            title.textContent = '编辑服务器';
            this.currentEditingId = server.id;
            this.fillForm(server);
        } else {
            title.textContent = '添加服务器';
            this.currentEditingId = null;
            form.reset();
            this.clearMappings();
        }

        modal.style.display = 'block';
    }

    hideModal() {
        document.getElementById('serverModal').style.display = 'none';
        this.currentEditingId = null;
    }

    fillForm(server) {
        document.getElementById('serverName').value = server.name;
        document.getElementById('serverPort').value = server.port;
        document.getElementById('autoStart').checked = server.autoStart;

        this.clearMappings();
        server.pathMappings.forEach(mapping => {
            this.addMappingRow(mapping);
        });
    }

    clearMappings() {
        document.getElementById('pathMappings').innerHTML = '';
    }

    addMappingRow(mapping = null) {
        const template = document.getElementById('mappingTemplate');
        const row = template.content.cloneNode(true);

        if (mapping) {
            row.querySelector('.url-path').value = mapping.urlPath;
            row.querySelector('.local-path').value = mapping.localPath;
            row.querySelector('.description').value = mapping.description || '';
        }

        row.querySelector('.remove-mapping').addEventListener('click', (e) => {
            e.target.closest('.mapping-item').remove();
        });

        document.getElementById('pathMappings').appendChild(row);
    }

    async saveServer() {
        const formData = new FormData(document.getElementById('serverForm'));
        const mappings = this.collectMappings();

        const serverData = {
            name: formData.get('name'),
            port: parseInt(formData.get('port')),
            autoStart: formData.has('autoStart'),
            pathMappings: mappings
        };

        try {
            let response;
            if (this.currentEditingId) {
                response = await fetch(`/api/servers/${this.currentEditingId}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(serverData)
                });
            } else {
                response = await fetch('/api/servers', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(serverData)
                });
            }

            const result = await response.json();
            if (result.success) {
                this.hideModal();
                this.loadServers();
                this.showSuccess(this.currentEditingId ? '服务器更新成功' : '服务器创建成功');
            } else {
                this.showError(result.error);
            }
        } catch (error) {
            this.showError('保存失败: ' + error.message);
        }
    }

    collectMappings() {
        const mappings = [];
        const mappingItems = document.querySelectorAll('.mapping-item');

        mappingItems.forEach(item => {
            const urlPath = item.querySelector('.url-path').value.trim();
            const localPath = item.querySelector('.local-path').value.trim();
            const description = item.querySelector('.description').value.trim();

            if (urlPath && localPath) {
                mappings.push({
                    id: Date.now().toString() + Math.random().toString(36).substr(2),
                    urlPath: urlPath.startsWith('/') ? urlPath : '/' + urlPath,
                    localPath,
                    description
                });
            }
        });

        return mappings;
    }

    async startServer(id) {
        try {
            const response = await fetch(`/api/servers/${id}/start`, {
                method: 'POST'
            });
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('服务器启动成功');
                this.loadServers();
            } else {
                this.showError('启动失败: ' + result.error);
            }
        } catch (error) {
            this.showError('启动失败: ' + error.message);
        }
    }

    async stopServer(id) {
        try {
            const response = await fetch(`/api/servers/${id}/stop`, {
                method: 'POST'
            });
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('服务器停止成功');
                this.loadServers();
            } else {
                this.showError('停止失败: ' + result.error);
            }
        } catch (error) {
            this.showError('停止失败: ' + error.message);
        }
    }

    editServer(server) {
        this.showModal(server);
    }

    async deleteServer(id) {
        if (!confirm('确定要删除这个服务器吗？')) {
            return;
        }

        try {
            const response = await fetch(`/api/servers/${id}`, {
                method: 'DELETE'
            });
            const result = await response.json();
            
            if (result.success) {
                this.showSuccess('服务器删除成功');
                this.loadServers();
            } else {
                this.showError('删除失败: ' + result.error);
            }
        } catch (error) {
            this.showError('删除失败: ' + error.message);
        }
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type) {
        // 简单的通知实现
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 4px;
            color: white;
            z-index: 10000;
            max-width: 300px;
            background-color: ${type === 'success' ? '#27ae60' : '#e74c3c'};
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
    new ServerManager();
});
