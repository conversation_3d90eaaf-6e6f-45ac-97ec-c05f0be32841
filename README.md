# HTTP服务器管理工具

一个可视化的HTTP文件服务器管理工具，功能类似于IIS管理器，支持创建和管理多个HTTP服务器实例，每个实例可以配置多个虚拟路径映射。

## 功能特性

- 🚀 创建和管理多个HTTP服务器实例
- 📁 支持多个虚拟路径映射
- 🎛️ 图形化Web管理界面
- ⚡ 实时服务器状态监控
- 💾 配置自动保存和恢复
- 🔄 支持服务器启动/停止控制

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 启动管理工具

```bash
npm start
```

或者使用开发模式（自动重启）：

```bash
npm run dev
```

### 3. 访问管理界面

打开浏览器访问：http://localhost:3000

## 使用说明

### 创建服务器

1. 点击"添加服务器"按钮
2. 填写服务器名称和端口号
3. 添加路径映射（可选）
4. 点击"保存"

### 配置路径映射

路径映射允许你将URL路径映射到本地文件夹：

- **URL路径**: 访问的URL路径，如 `/docs`
- **本地路径**: 对应的本地文件夹，如 `C:\Documents`
- **描述**: 可选的描述信息

### 示例配置

创建一个服务器，端口8080，配置以下路径映射：

- `/docs` → `C:\Documents`
- `/images` → `D:\Pictures`
- `/files` → `E:\SharedFiles`

访问示例：
- `http://localhost:8080/docs/file.pdf` → `C:\Documents\file.pdf`
- `http://localhost:8080/images/photo.jpg` → `D:\Pictures\photo.jpg`

### 服务器管理

- **启动**: 点击绿色"启动"按钮
- **停止**: 点击橙色"停止"按钮
- **编辑**: 点击蓝色"编辑"按钮修改配置
- **删除**: 点击红色"删除"按钮（会先停止服务器）

## 技术架构

- **后端**: Node.js + Express
- **前端**: HTML + CSS + JavaScript
- **配置存储**: JSON文件
- **静态文件服务**: Express.static

## 项目结构

```
httpServer/
├── src/
│   ├── app.js              # 主应用入口
│   ├── core/
│   │   ├── ServerManager.js # 服务器管理核心
│   │   └── ConfigManager.js # 配置管理
│   └── routes/
│       └── api.js          # API路由
├── public/
│   ├── index.html          # 主页面
│   ├── styles.css          # 样式文件
│   └── script.js           # 前端脚本
├── config/
│   └── servers.json        # 服务器配置（自动生成）
└── package.json
```

## API接口

### 服务器管理

- `GET /api/servers` - 获取所有服务器
- `POST /api/servers` - 创建新服务器
- `PUT /api/servers/:id` - 更新服务器配置
- `DELETE /api/servers/:id` - 删除服务器

### 服务器控制

- `POST /api/servers/:id/start` - 启动服务器
- `POST /api/servers/:id/stop` - 停止服务器

### 路径映射

- `POST /api/servers/:id/mappings` - 添加路径映射
- `DELETE /api/servers/:id/mappings/:mappingId` - 删除路径映射

## 注意事项

1. 确保配置的本地路径存在且有读取权限
2. 避免端口冲突，每个服务器使用不同的端口
3. 管理工具默认运行在3000端口，请确保该端口未被占用
4. 配置会自动保存到 `config/servers.json` 文件

## 故障排除

### 端口被占用
如果遇到端口被占用的错误，请检查：
1. 是否有其他程序使用了该端口
2. 是否已经创建了使用相同端口的服务器

### 路径访问失败
如果无法访问文件，请检查：
1. 本地路径是否存在
2. 是否有读取权限
3. 路径格式是否正确（Windows使用反斜杠）

### 服务器无法启动
请检查：
1. 端口是否被占用
2. 配置的路径映射是否有效
3. 查看控制台错误信息
