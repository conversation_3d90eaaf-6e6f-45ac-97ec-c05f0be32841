const express = require('express');
const router = express.Router();

module.exports = (serverManager) => {
    // 获取所有服务器
    router.get('/servers', (req, res) => {
        try {
            const servers = serverManager.getAllServers();
            res.json({ success: true, data: servers });
        } catch (error) {
            res.status(500).json({ success: false, error: error.message });
        }
    });

    // 获取单个服务器
    router.get('/servers/:id', (req, res) => {
        try {
            const server = serverManager.getServer(req.params.id);
            if (!server) {
                return res.status(404).json({ success: false, error: '服务器不存在' });
            }
            res.json({ success: true, data: server });
        } catch (error) {
            res.status(500).json({ success: false, error: error.message });
        }
    });

    // 创建新服务器
    router.post('/servers', (req, res) => {
        try {
            const { name, port, pathMappings, autoStart } = req.body;
            
            // 验证必需字段
            if (!port) {
                return res.status(400).json({ success: false, error: '端口号是必需的' });
            }

            // 检查端口是否已被使用
            const existingServers = serverManager.getAllServers();
            const portInUse = existingServers.some(server => server.port === port);
            if (portInUse) {
                return res.status(400).json({ success: false, error: '端口已被使用' });
            }

            const serverConfig = {
                name,
                port: parseInt(port),
                pathMappings: pathMappings || [],
                autoStart: autoStart || false
            };

            const newServer = serverManager.createServer(serverConfig);
            res.status(201).json({ success: true, data: newServer });
        } catch (error) {
            res.status(500).json({ success: false, error: error.message });
        }
    });

    // 更新服务器配置
    router.put('/servers/:id', (req, res) => {
        try {
            const { name, port, pathMappings, autoStart } = req.body;
            
            const updates = {};
            if (name !== undefined) updates.name = name;
            if (port !== undefined) {
                // 检查新端口是否被其他服务器使用
                const existingServers = serverManager.getAllServers();
                const portInUse = existingServers.some(server => 
                    server.port === parseInt(port) && server.id !== req.params.id
                );
                if (portInUse) {
                    return res.status(400).json({ success: false, error: '端口已被使用' });
                }
                updates.port = parseInt(port);
            }
            if (pathMappings !== undefined) updates.pathMappings = pathMappings;
            if (autoStart !== undefined) updates.autoStart = autoStart;

            const updatedServer = serverManager.updateServer(req.params.id, updates);
            if (!updatedServer) {
                return res.status(404).json({ success: false, error: '服务器不存在' });
            }

            res.json({ success: true, data: updatedServer });
        } catch (error) {
            res.status(500).json({ success: false, error: error.message });
        }
    });

    // 删除服务器
    router.delete('/servers/:id', (req, res) => {
        try {
            const success = serverManager.deleteServer(req.params.id);
            if (!success) {
                return res.status(404).json({ success: false, error: '服务器不存在' });
            }
            res.json({ success: true, message: '服务器已删除' });
        } catch (error) {
            res.status(500).json({ success: false, error: error.message });
        }
    });

    // 启动服务器
    router.post('/servers/:id/start', (req, res) => {
        try {
            serverManager.startServer(req.params.id);
            res.json({ success: true, message: '服务器已启动' });
        } catch (error) {
            res.status(500).json({ success: false, error: error.message });
        }
    });

    // 停止服务器
    router.post('/servers/:id/stop', (req, res) => {
        try {
            serverManager.stopServer(req.params.id);
            res.json({ success: true, message: '服务器已停止' });
        } catch (error) {
            res.status(500).json({ success: false, error: error.message });
        }
    });

    // 添加路径映射
    router.post('/servers/:id/mappings', (req, res) => {
        try {
            const { urlPath, localPath, description } = req.body;
            
            if (!urlPath || !localPath) {
                return res.status(400).json({ 
                    success: false, 
                    error: 'URL路径和本地路径都是必需的' 
                });
            }

            const server = serverManager.getServer(req.params.id);
            if (!server) {
                return res.status(404).json({ success: false, error: '服务器不存在' });
            }

            const newMapping = {
                id: Date.now().toString(),
                urlPath: urlPath.startsWith('/') ? urlPath : '/' + urlPath,
                localPath,
                description: description || ''
            };

            const pathMappings = [...server.pathMappings, newMapping];
            const updatedServer = serverManager.updateServer(req.params.id, { pathMappings });

            res.json({ success: true, data: newMapping });
        } catch (error) {
            res.status(500).json({ success: false, error: error.message });
        }
    });

    // 删除路径映射
    router.delete('/servers/:id/mappings/:mappingId', (req, res) => {
        try {
            const server = serverManager.getServer(req.params.id);
            if (!server) {
                return res.status(404).json({ success: false, error: '服务器不存在' });
            }

            const pathMappings = server.pathMappings.filter(
                mapping => mapping.id !== req.params.mappingId
            );

            serverManager.updateServer(req.params.id, { pathMappings });
            res.json({ success: true, message: '路径映射已删除' });
        } catch (error) {
            res.status(500).json({ success: false, error: error.message });
        }
    });

    return router;
};
