<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTTP服务器管理工具</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>HTTP服务器管理工具</h1>
            <button id="addServerBtn" class="btn btn-primary">添加服务器</button>
        </header>

        <main>
            <div class="servers-grid" id="serversGrid">
                <!-- 服务器卡片将在这里动态生成 -->
            </div>
        </main>
    </div>

    <!-- 添加/编辑服务器模态框 -->
    <div id="serverModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">添加服务器</h2>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <form id="serverForm">
                    <div class="form-group">
                        <label for="serverName">服务器名称:</label>
                        <input type="text" id="serverName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="serverPort">端口:</label>
                        <input type="number" id="serverPort" name="port" min="1" max="65535" required>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="autoStart" name="autoStart">
                            自动启动
                        </label>
                    </div>
                    <div class="form-group">
                        <h3>路径映射</h3>
                        <div id="pathMappings">
                            <!-- 路径映射将在这里动态生成 -->
                        </div>
                        <button type="button" id="addMappingBtn" class="btn btn-secondary">添加路径映射</button>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" id="cancelBtn">取消</button>
                <button type="submit" form="serverForm" class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>

    <!-- 路径映射模板 -->
    <template id="mappingTemplate">
        <div class="mapping-item">
            <div class="form-row">
                <div class="form-group">
                    <label>URL路径:</label>
                    <input type="text" class="url-path" placeholder="/docs" required>
                </div>
                <div class="form-group">
                    <label>本地路径:</label>
                    <input type="text" class="local-path" placeholder="C:\Documents" required>
                </div>
                <div class="form-group">
                    <label>描述:</label>
                    <input type="text" class="description" placeholder="文档目录">
                </div>
                <button type="button" class="btn btn-danger remove-mapping">删除</button>
            </div>
        </div>
    </template>

    <!-- 服务器卡片模板 -->
    <template id="serverCardTemplate">
        <div class="server-card">
            <div class="server-header">
                <h3 class="server-name"></h3>
                <div class="server-status">
                    <span class="status-indicator"></span>
                    <span class="status-text"></span>
                </div>
            </div>
            <div class="server-info">
                <p><strong>端口:</strong> <span class="server-port"></span></p>
                <p><strong>路径映射:</strong> <span class="mapping-count"></span> 个</p>
            </div>
            <div class="server-actions">
                <button class="btn btn-success start-btn">启动</button>
                <button class="btn btn-warning stop-btn">停止</button>
                <button class="btn btn-info edit-btn">编辑</button>
                <button class="btn btn-danger delete-btn">删除</button>
            </div>
            <div class="server-mappings">
                <h4>路径映射:</h4>
                <ul class="mappings-list"></ul>
            </div>
        </div>
    </template>

    <script src="script.js"></script>
</body>
</html>
